# Technology Stack

## Build System & Package Management
- **Primary**: `uv` (Python package manager) - preferred for development and deployment
- **Alternative**: `pip` with virtual environments
- **Build Backend**: `hatchling` (modern Python packaging)

## Core Technologies
- **Python**: 3.10+ required (supports 3.10, 3.11, 3.12, 3.13)
- **FastMCP**: 2.2.0+ - MCP server framework
- **httpx**: 0.25.0+ - Async HTTP client for API requests
- **Pydantic**: 2.0.0+ - Data validation and settings management
- **python-dotenv**: Environment variable management

## Architecture Patterns
- **Async/Await**: All API operations are asynchronous
- **Context Managers**: Proper resource management with `async with`
- **Dependency Injection**: Configuration passed to components
- **Token Management**: Automatic OAuth2 refresh with buffer time
- **Retry Logic**: Exponential backoff for failed requests
- **Clean Architecture**: Separation of concerns (auth, client, config, server)

## Common Commands

### Development Setup
```bash
# Preferred method
uv sync

# Alternative method
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -e .
```

### Running the Server
```bash
# Production
albatross-kiotviet-mcp-server

# With uv
uv run albatross-kiotviet-mcp-server

# Development mode
fastmcp dev src/albatross_kiotviet_mcp/server.py
```

### Testing
```bash
# Run example usage
python example_usage.py

# Test MCP tools
python test_mcp_tools.py

# Test server functionality
python test_mcp_server.py
```

### Configuration
```bash
# Setup environment
cp .env.example .env
# Edit .env with your KiotViet credentials
```

## Code Style & Conventions
- **Type Hints**: Required for all function parameters and return types
- **Docstrings**: Google-style docstrings for all public methods
- **Error Handling**: Comprehensive try/catch with specific error messages
- **Logging**: Structured logging with emojis for readability
- **Async Context**: Always use `async with` for HTTP clients
- **Configuration**: Environment-based config with Pydantic validation