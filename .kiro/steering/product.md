# Product Overview

**Albatross KiotViet MCP Server** is a Model Context Protocol (MCP) server that integrates with KiotViet's Public API. It enables AI assistants to interact with KiotViet's retail management system through natural language queries.

## Core Purpose
- Provides secure, authenticated access to KiotViet retail data
- Enables AI tools to query product categories, inventory, and retail information
- Serves as a bridge between AI assistants (like <PERSON>) and KiotViet's API

## Key Features
- **Secure Authentication**: Automatic OAuth2 token management with refresh handling
- **Product Categories**: Retrieve and search product categories with pagination
- **Rate Limiting**: Built-in request throttling and retry logic with exponential backoff
- **Production Ready**: Clean architecture, comprehensive error handling, and structured logging
- **Extensible**: Designed for easy addition of new KiotViet API endpoints

## Target Users
- Developers integrating KiotViet with AI assistants
- Retail businesses using KiotViet who want AI-powered data access
- Teams building custom retail management tools with AI capabilities

## Integration Points
- **Claude Desktop**: Primary integration target via MCP protocol
- **FastMCP**: Built on FastMCP framework for MCP server functionality
- **KiotViet Public API**: Connects to KiotViet's retail management platform