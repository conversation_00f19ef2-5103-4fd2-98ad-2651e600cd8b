# Project Structure

## Root Directory Layout
```
albatross-kiotviet-mcp/
├── .env                    # Environment variables (not in git)
├── .env.example           # Environment template
├── .gitignore             # Git ignore rules
├── README.md              # Main documentation
├── CHANGELOG.md           # Version history
├── TROUBLESHOOTING.md     # Common issues and solutions
├── pyproject.toml         # Python project configuration
├── requirements.txt       # Python dependencies
├── uv.lock               # UV lock file for reproducible builds
├── mcp.json              # MCP server configuration
├── example_usage.py      # Usage examples and testing
├── monitor_logs.py       # Log monitoring utility
├── test_mcp_server.py    # Server functionality tests
├── test_mcp_tools.py     # MCP tools tests
├── logs/                 # Application logs (auto-created)
└── src/                  # Source code
```

## Source Code Organization
```
src/albatross_kiotviet_mcp/
├── __init__.py           # Package initialization
├── server.py             # FastMCP server and tool definitions
├── client.py             # KiotViet API client
├── auth.py               # Authentication and token management
└── config.py             # Configuration management
```

## Key Components

### Core Modules
- **server.py**: Entry point, FastMCP server setup, tool definitions
- **client.py**: HTTP client for KiotViet API with retry logic
- **auth.py**: OAuth2 token management with automatic refresh
- **config.py**: Pydantic-based configuration with environment variables

### Configuration Files
- **.env**: Local environment variables (never commit)
- **.env.example**: Template for required environment variables
- **pyproject.toml**: Python packaging, dependencies, and metadata
- **mcp.json**: MCP server configuration for Claude Desktop

### Testing & Examples
- **example_usage.py**: Direct API client usage examples
- **test_mcp_server.py**: Server functionality testing
- **test_mcp_tools.py**: MCP tool testing
- **monitor_logs.py**: Log file monitoring utility

## File Naming Conventions
- **Snake_case**: All Python files and modules
- **Lowercase**: Configuration files (.env, pyproject.toml)
- **UPPERCASE**: Documentation files (README.md, CHANGELOG.md)
- **Descriptive names**: Files clearly indicate their purpose

## Directory Conventions
- **src/**: All source code under src layout
- **logs/**: Auto-created for application logs
- **.venv/**: Virtual environment (not in git)
- **__pycache__/**: Python cache directories (not in git)

## Import Structure
- **Relative imports**: Within package (`from .config import get_config`)
- **Absolute imports**: External packages (`from fastmcp import FastMCP`)
- **Standard library**: First, then third-party, then local imports

## Logging Structure
- **Daily log files**: `logs/kiotviet_mcp_YYYYMMDD.log`
- **Structured format**: Timestamp, logger name, level, message
- **Emoji indicators**: For easy visual scanning of log levels
- **Both file and console**: Dual output for development and production