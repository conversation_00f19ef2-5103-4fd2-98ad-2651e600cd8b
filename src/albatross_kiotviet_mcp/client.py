"""KiotViet API client."""

import asyncio
import logging
from typing import Dict, Any, Optional, List
import httpx
from .config import KiotVietConfig
from .auth import TokenManager

logger = logging.getLogger(__name__)


class KiotVietAPIClient:
    """Client for interacting with KiotViet Public API."""
    
    def __init__(self, config: KiotVietConfig):
        self.config = config
        self.token_manager = TokenManager(config)
        self._client: Optional[httpx.AsyncClient] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self._client = httpx.AsyncClient(
            base_url=self.config.api_base_url,
            timeout=self.config.request_timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._client:
            await self._client.aclose()
    
    async def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests."""
        access_token = await self.token_manager.get_access_token()
        return {
            "Retailer": self.config.retailer,
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make an authenticated request to KiotViet API."""
        if not self._client:
            raise RuntimeError("Client not initialized. Use async context manager.")
        
        headers = await self._get_headers()
        url = endpoint if endpoint.startswith('http') else f"{self.config.api_base_url}{endpoint}"
        
        for attempt in range(self.config.max_retries):
            try:
                response = await self._client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    json=data,
                    params=params
                )
                
                if response.status_code == 401:
                    # Token might be expired, invalidate and retry once
                    if attempt == 0:
                        logger.warning("Received 401, invalidating token and retrying...")
                        self.token_manager.invalidate_token()
                        headers = await self._get_headers()
                        continue
                
                response.raise_for_status()
                return response.json()
                
            except httpx.HTTPError as e:
                logger.error(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt == self.config.max_retries - 1:
                    raise Exception(f"API request failed after {self.config.max_retries} attempts: {e}")
                
                # Wait before retry (exponential backoff)
                await asyncio.sleep(2 ** attempt)
    
    async def get_categories(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        hierarchical_data: bool = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API.
        
        Args:
            page_size: Number of items per page (max 100)
            current_item: Starting item index for pagination
            order_direction: Sort order ("Asc" or "Desc")
            hierarchical_data: Whether to return hierarchical structure
            
        Returns:
            API response containing categories data
        """
        data = {
            "pageSize": min(page_size, 100),  # Ensure max limit
            "currentItem": current_item,
            "orderDirection": order_direction,
            "hierachicalData": hierarchical_data
        }
        
        return await self._make_request("GET", "/categories", data=data)