"""Base MCP tool class."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class BaseMCPTool(ABC):
    """Base class for all MCP tools."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute the tool with given parameters.
        
        Args:
            **kwargs: Tool-specific parameters
            
        Returns:
            Tool execution result
        """
        pass
    
    def validate_parameters(self, **kwargs) -> None:
        """Validate tool parameters.
        
        Args:
            **kwargs: Parameters to validate
            
        Raises:
            ValueError: If parameters are invalid
        """
        # Base implementation - override in subclasses
        pass
    
    async def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
        """Handle tool execution errors.
        
        Args:
            error: The error that occurred
            context: Additional context information
        """
        self.logger.error(f"Tool {self.name} failed: {error}")
        if context:
            self.logger.error(f"Context: {context}")
        
        # Re-raise with more context
        raise Exception(f"Tool '{self.name}' execution failed: {str(error)}")
