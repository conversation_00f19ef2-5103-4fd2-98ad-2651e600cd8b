"""KiotViet MCP Server."""

import logging
from typing import Dict, Any, Optional
from fastmcp import FastMCP

from .infrastructure.config.settings import get_config
from .infrastructure.api.kiotviet_client import KiotVietAPIClient
from .tools.categories.categories_tool import CategoriesTool
from .tools.products.products_tool import ProductsTool
from .tools.branches.branches_tool import BranchesTool

logger = logging.getLogger(__name__)

# Initialize FastMCP server
server = FastMCP(name="KiotVietMCPServer")

# Global instances (lazy-loaded)
_kiotviet_client: Optional[KiotVietAPIClient] = None
_config = None
_categories_tool: Optional[CategoriesTool] = None
_products_tool: Optional[ProductsTool] = None
_branches_tool: Optional[BranchesTool] = None


def get_kiotviet_client() -> KiotVietAPIClient:
    """Get or create KiotViet API client instance."""
    global _kiotviet_client, _config

    if _kiotviet_client is None:
        _config = get_config()
        _kiotviet_client = KiotVietAPIClient(_config)

    return _kiotviet_client


def get_categories_tool() -> CategoriesTool:
    """Get or create categories tool instance."""
    global _categories_tool

    if _categories_tool is None:
        client = get_kiotviet_client()
        _categories_tool = CategoriesTool(client)

    return _categories_tool


def get_products_tool() -> ProductsTool:
    """Get or create products tool instance."""
    global _products_tool

    if _products_tool is None:
        client = get_kiotviet_client()
        _products_tool = ProductsTool(client)

    return _products_tool


def get_branches_tool() -> BranchesTool:
    """Get or create branches tool instance."""
    global _branches_tool

    if _branches_tool is None:
        client = get_kiotviet_client()
        _branches_tool = BranchesTool(client)

    return _branches_tool


# MCP Tool Definitions

@server.tool(
    name="get_categories",
    description="Get product categories from KiotViet API with pagination support"
)
async def get_categories(
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc",
    hierarchical_data: bool = False
) -> Dict[str, Any]:
    """Get product categories from KiotViet API.

    Args:
        page_size: Number of items per page (default: 50, max: 100)
        current_item: Starting item index for pagination (default: 0)
        order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
        hierarchical_data: Whether to return hierarchical structure (default: False)

    Returns:
        Dictionary containing categories data and pagination info
    """
    tool = get_categories_tool()
    return await tool.execute(
        page_size=page_size,
        current_item=current_item,
        order_direction=order_direction,
        hierarchical_data=hierarchical_data
    )


@server.tool(
    name="get_products",
    description="Get products from KiotViet API with pagination and filtering support"
)
async def get_products(
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc",
    category_id: Optional[int] = None,
    include_inventory: bool = False
) -> Dict[str, Any]:
    """Get products from KiotViet API.

    Args:
        page_size: Number of items per page (default: 50, max: 100)
        current_item: Starting item index for pagination (default: 0)
        order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
        category_id: Filter by category ID (optional)
        include_inventory: Include inventory information (default: False)

    Returns:
        Dictionary containing products data and pagination info
    """
    tool = get_products_tool()
    return await tool.execute(
        page_size=page_size,
        current_item=current_item,
        order_direction=order_direction,
        category_id=category_id,
        include_inventory=include_inventory
    )


@server.tool(
    name="get_branches",
    description="Get branches from KiotViet API with pagination support"
)
async def get_branches(
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc"
) -> Dict[str, Any]:
    """Get branches from KiotViet API.

    Args:
        page_size: Number of items per page (default: 50, max: 100)
        current_item: Starting item index for pagination (default: 0)
        order_direction: Sort order - "Asc" or "Desc" (default: "Asc")

    Returns:
        Dictionary containing branches data and pagination info
    """
    tool = get_branches_tool()
    return await tool.execute(
        page_size=page_size,
        current_item=current_item,
        order_direction=order_direction
    )


def run():
    """Entry point for running the FastMCP server."""
    logger.info(f"Starting {server.name}...")

    # Validate configuration on startup
    try:
        config = get_config()
        logger.info(f"Configuration loaded successfully for retailer: {config.retailer}")
        logger.info("Available tools: get_categories, get_products, get_branches")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        raise

    return server.run()