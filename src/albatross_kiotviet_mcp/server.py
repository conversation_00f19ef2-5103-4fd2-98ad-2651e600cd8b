"""KiotViet MCP Server."""

import asyncio
import logging
import os
from typing import Dict, Any, Optional
from datetime import datetime
from fastmcp import FastMCP
from dotenv import load_dotenv

from .config import get_config
from .client import KiotVietAPIClient

# Load environment variables
load_dotenv()

# Create logs directory if it doesn't exist
# Get the project root directory (where pyproject.toml is)
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
log_dir = os.path.join(project_root, 'logs')
os.makedirs(log_dir, exist_ok=True)

# Configure logging to both file and console
log_file = os.path.join(log_dir, f'kiotviet_mcp_{datetime.now().strftime("%Y%m%d")}.log')
print(f"📁 Log file: {log_file}")  # Debug print

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Initialize FastMCP server
server = FastMCP(name="KiotVietMCPServer")

# Global client instance (lazy-loaded)
_kiotviet_client: Optional[KiotVietAPIClient] = None
_config = None


def get_kiotviet_client() -> KiotVietAPIClient:
    """Get or create KiotViet API client instance."""
    global _kiotviet_client, _config
    
    if _kiotviet_client is None:
        _config = get_config()
        _kiotviet_client = KiotVietAPIClient(_config)
    
    return _kiotviet_client


@server.tool(
    name="get_categories",
    description="Get product categories from KiotViet API with pagination support"
)
async def get_categories(
    page_size: int = 50,
    current_item: int = 0,
    order_direction: str = "Asc",
    hierarchical_data: bool = False
) -> Dict[str, Any]:
    """Get product categories from KiotViet API.
    
    Args:
        page_size: Number of items per page (default: 50, max: 100)
        current_item: Starting item index for pagination (default: 0)
        order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
        hierarchical_data: Whether to return hierarchical structure (default: False)
    
    Returns:
        Dictionary containing categories data and pagination info
    """
    
    try:
        client = get_kiotviet_client()
        
        async with client:
            result = await client.get_categories(
                page_size=page_size or 50,
                current_item=current_item or 0,
                order_direction=order_direction or "Asc",
                hierarchical_data=hierarchical_data or False
            )
            return result
            
    except Exception as e:
        raise Exception(f"Failed to retrieve categories: {str(e)}")


def run():
    """Entry point for running the FastMCP server."""
    logger.info(f"Starting {server.name}...")
    
    # Validate configuration on startup
    try:
        config = get_config()
        logger.info(f"Configuration loaded successfully for retailer: {config.retailer}")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        raise
    
    return server.run()


if __name__ == "__main__":
    # For direct execution
    asyncio.run(run())