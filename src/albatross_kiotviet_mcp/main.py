"""Main entry point for the KiotViet MCP Server."""

import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv

from .server import run

# Load environment variables
load_dotenv()

# Create logs directory if it doesn't exist
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
log_dir = os.path.join(project_root, 'logs')
os.makedirs(log_dir, exist_ok=True)

# Configure logging to both file and console
log_file = os.path.join(log_dir, f'kiotviet_mcp_{datetime.now().strftime("%Y%m%d")}.log')
print(f"📁 Log file: {log_file}")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main entry point."""
    logger.info("Starting Albatross KiotViet MCP Server...")
    return run()


if __name__ == "__main__":
    asyncio.run(main())
