"""Authentication management for KiotViet API."""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import httpx
from ..config.settings import KiotVietConfig

logger = logging.getLogger(__name__)


class TokenManager:
    """Manages KiotViet API authentication tokens."""
    
    def __init__(self, config: KiotVietConfig):
        self.config = config
        self._access_token: Optional[str] = None
        self._token_expires_at: Optional[datetime] = None
        self._lock = asyncio.Lock()
    
    async def get_access_token(self) -> str:
        """Get a valid access token, refreshing if necessary."""
        async with self._lock:
            if self._is_token_valid():
                return self._access_token
            
            logger.info("Access token expired or missing, refreshing...")
            await self._refresh_token()
            return self._access_token
    
    def _is_token_valid(self) -> bool:
        """Check if current token is valid and not expiring soon."""
        if not self._access_token or not self._token_expires_at:
            return False
        
        # Check if token expires within buffer time
        buffer_time = timedelta(seconds=self.config.token_buffer_seconds)
        return datetime.now() + buffer_time < self._token_expires_at
    
    async def _refresh_token(self) -> None:
        """Refresh the access token from KiotViet API."""
        try:
            async with httpx.AsyncClient(timeout=self.config.request_timeout) as client:
                response = await client.post(
                    self.config.auth_url,
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded"
                    },
                    data={
                        "scopes": "PublicApi.Access",
                        "grant_type": "client_credentials",
                        "client_id": self.config.client_id,
                        "client_secret": self.config.client_secret
                    }
                )
                response.raise_for_status()
                
                token_data = response.json()
                self._access_token = token_data["access_token"]
                expires_in = token_data.get("expires_in", 3600)  # Default 1 hour
                self._token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                logger.info(f"Token refreshed successfully, expires at: {self._token_expires_at}")
                
        except httpx.HTTPError as e:
            logger.error(f"Failed to refresh token: {e}")
            raise Exception(f"Authentication failed: {e}")
        except KeyError as e:
            logger.error(f"Invalid token response format: {e}")
            raise Exception(f"Invalid authentication response: {e}")
    
    def invalidate_token(self) -> None:
        """Invalidate current token to force refresh on next request."""
        self._access_token = None
        self._token_expires_at = None
