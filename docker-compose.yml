version: '3.8'

services:
  kiotviet-mcp:
    build: .
    container_name: albatross-kiotviet-mcp
    environment:
      # KiotViet API credentials - set these in your .env file or environment
      - KIOTVIET_CLIENT_ID=${KIOTVIET_CLIENT_ID}
      - KIOTVIET_CLIENT_SECRET=${KIOTVIET_CLIENT_SECRET}
      - KIOTVIET_RETAILER=${KIOTVIET_RETAILER}
      
      # Optional configuration
      - KIOTVIET_AUTH_URL=${KIOTVIET_AUTH_URL:-https://id.kiotviet.vn/connect/token}
      - KIOTVIET_API_BASE_URL=${KIOTVIET_API_BASE_URL:-https://public.kiotapi.com}
      - KIOTVIET_TOKEN_BUFFER_SECONDS=${KIOTVIET_TOKEN_BUFFER_SECONDS:-300}
      - KIOTVIET_REQUEST_TIMEOUT=${KIOTVIET_REQUEST_TIMEOUT:-30}
      - KIO<PERSON>IET_MAX_RETRIES=${KIOTVIET_MAX_RETRIES:-3}
      
      # Python configuration
      - PYTHONUNBUFFERED=1
    volumes:
      # <PERSON> logs directory for persistence
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "from src.albatross_kiotviet_mcp.infrastructure.config.settings import get_config; get_config()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # Uncomment if you need to expose ports for debugging
    # ports:
    #   - "8000:8000"
