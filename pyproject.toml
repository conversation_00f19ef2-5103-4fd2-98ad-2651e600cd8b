[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "albatross-kiotviet-mcp"
version = "0.1.0"
description = "KiotViet API integration for FastMCP"
readme = "README.md"
requires-python = ">=3.10"
license = "MIT"
authors = [
    { name = "Your Name", email = "<EMAIL>" }
]
dependencies = [
    "fastmcp>=2.2.0",
    "httpx>=0.25.0",
    "python-dotenv>=0.21.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0"
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[project.urls]
Homepage = "https://github.com/yourusername/albatross-kiotviet-mcp"
Issues = "https://github.com/yourusername/albatross-kiotviet-mcp/issues"

[project.scripts]
albatross-kiotviet-mcp-server = "albatross_kiotviet_mcp.server:run"