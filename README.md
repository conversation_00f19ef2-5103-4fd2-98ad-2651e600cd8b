# Albatross KiotViet MCP Server

A Model Context Protocol (MCP) server for integrating with KiotViet Public API. This server allows you to interact with KiotViet's retail management system through AI tools, enabling natural language queries for product categories, inventory, and more.

## Features

- **Secure Authentication**: Automatic token management with refresh handling
- **Product Categories**: Retrieve and search product categories with pagination
- **Rate Limiting**: Built-in request throttling and retry logic
- **Production Ready**: Clean architecture, error handling, and logging
- **Extensible**: Easy to add new KiotViet API endpoints

## Prerequisites

- **Python 3.10 or higher**
- **KiotViet Developer Account**: You need API credentials from KiotViet
  - Client ID
  - Client Secret  
  - Retailer name
- **FastMCP**: For MCP integration
- Optional: **Claude Desktop** for AI integration

## Installation

### Option 1: Install from Source

1. **Clone the Repository**:
   ```bash
   git clone <repository-url>
   cd albatross-kiotviet-mcp
   ```

2. **Set Up Virtual Environment**:
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install Dependencies**:
   ```bash
   pip install -e .
   ```

4. **Configure Environment Variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your KiotViet credentials
   ```

### Option 2: Install with uv (Recommended)

```bash
uv sync
```

## Configuration

Create a `.env` file in the project root with your KiotViet API credentials:

```env
KIOTVIET_CLIENT_ID=your_client_id_here
KIOTVIET_CLIENT_SECRET=your_client_secret_here
KIOTVIET_RETAILER=your_retailer_name_here
```

### Optional Configuration

```env
# API endpoints (defaults shown)
KIOTVIET_AUTH_URL=https://id.kiotviet.vn/connect/token
KIOTVIET_API_BASE_URL=https://public.kiotapi.com

# Token and request settings
KIOTVIET_TOKEN_BUFFER_SECONDS=300  # Refresh token 5 min before expiry
KIOTVIET_REQUEST_TIMEOUT=30        # Request timeout in seconds
KIOTVIET_MAX_RETRIES=3             # Max retry attempts
```

## Running the Server

### Using the CLI Script

```bash
albatross-kiotviet-mcp-server
```

### Using uv

```bash
uv run albatross-kiotviet-mcp-server
```

### Development Mode

```bash
fastmcp dev src/albatross_kiotviet_mcp/server.py
```

## Using with Claude Desktop

### Step 1: Configure Claude Desktop

Edit your Claude Desktop configuration file:

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

### Step 2: Add Server Configuration

```json
{
  "mcpServers": {
    "kiotviet": {
      "command": "uv",
      "args": [
        "--directory",
        "/path/to/albatross-kiotviet-mcp",
        "run",
        "albatross-kiotviet-mcp-server"
      ],
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

### Step 3: Restart Claude Desktop

Quit and reopen Claude Desktop to load the new configuration.

## Available Tools

### `get_categories`

Retrieve product categories from KiotViet with pagination support.

**Parameters:**
- `page_size` (optional): Number of items per page (default: 50, max: 100)
- `current_item` (optional): Starting item index for pagination (default: 0)
- `order_direction` (optional): Sort order - "Asc" or "Desc" (default: "Asc")
- `hierarchical_data` (optional): Whether to return hierarchical structure (default: False)

**Claude Desktop Examples:**

```
Get all product categories from KiotViet
```

```
Show me the first 20 product categories in descending order
```

```
Get product categories with hierarchical structure, starting from item 50
```

## Architecture

The server is built with a clean, modular architecture:

```
src/albatross_kiotviet_mcp/
├── __init__.py          # Package initialization
├── server.py            # FastMCP server and tools
├── client.py            # KiotViet API client
├── auth.py              # Authentication and token management
└── config.py            # Configuration management
```

### Key Components

- **TokenManager**: Handles automatic token refresh and validation
- **KiotVietAPIClient**: Clean API client with retry logic and error handling
- **Configuration**: Environment-based configuration with validation
- **Server**: FastMCP integration with tool definitions

## Adding New API Endpoints

The codebase is designed for easy extension. To add a new KiotViet API endpoint:

1. **Add method to `KiotVietAPIClient`**:
   ```python
   async def get_products(self, **kwargs) -> Dict[str, Any]:
       return await self._make_request("GET", "/products", data=kwargs)
   ```

2. **Add tool to `server.py`**:
   ```python
   @server.tool(name="get_products", description="Get products from KiotViet")
   async def get_products(**kwargs) -> Dict[str, Any]:
       client = get_kiotviet_client()
       async with client:
           return await client.get_products(**kwargs)
   ```

## Error Handling

The server includes comprehensive error handling:

- **Authentication errors**: Automatic token refresh on 401 responses
- **Network errors**: Retry logic with exponential backoff
- **Configuration errors**: Validation on startup
- **API errors**: Detailed error messages and logging

## Troubleshooting

### Server Not Starting

- Check your `.env` file has all required credentials
- Verify KiotViet API credentials are correct
- Check logs for specific error messages

### Authentication Errors

- Verify `KIOTVIET_CLIENT_ID` and `KIOTVIET_CLIENT_SECRET` are correct
- Ensure your KiotViet account has API access enabled
- Check if `KIOTVIET_RETAILER` name matches your account

### Claude Desktop Integration

- Verify the path in `claude_desktop_config.json` is correct
- Restart Claude Desktop after configuration changes
- Check Claude's MCP logs for connection errors

## Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check the troubleshooting section
- Review KiotViet API documentation
- Open an issue on GitHub