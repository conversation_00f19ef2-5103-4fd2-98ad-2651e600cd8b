"""Tests for KiotViet API client."""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
import httpx
from src.albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient
from src.albatross_kiotviet_mcp.infrastructure.config.settings import KiotVietConfig


class TestKiotVietAPIClient:
    """Test cases for KiotVietAPIClient."""
    
    @pytest.fixture
    def client(self, mock_config):
        """Create API client instance for testing."""
        return KiotVietAPIClient(mock_config)
    
    @pytest.mark.asyncio
    async def test_context_manager(self, client):
        """Test async context manager functionality."""
        async with client as c:
            assert c._client is not None
            assert isinstance(c._client, httpx.AsyncClient)
        
        # Client should be closed after exiting context
        assert client._client is None or client._client.is_closed
    
    @pytest.mark.asyncio
    async def test_get_headers(self, client):
        """Test header generation."""
        with patch.object(client.token_manager, 'get_access_token', return_value="test_token"):
            headers = await client._get_headers()
            
            expected_headers = {
                "Retailer": "test_retailer",
                "Authorization": "Bearer test_token",
                "Content-Type": "application/json"
            }
            assert headers == expected_headers
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, client, mock_config):
        """Test successful API request."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_response.raise_for_status = MagicMock()
        
        with patch.object(client, '_get_headers', return_value={"Authorization": "Bearer test"}):
            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_client.request = AsyncMock(return_value=mock_response)
                mock_client_class.return_value = mock_client
                
                client._client = mock_client
                
                result = await client.make_request("GET", "/test")
                
                assert result == {"success": True}
                mock_client.request.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_401_retry(self, client):
        """Test 401 error handling with token refresh."""
        mock_response_401 = MagicMock()
        mock_response_401.status_code = 401
        
        mock_response_success = MagicMock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = {"success": True}
        mock_response_success.raise_for_status = MagicMock()
        
        with patch.object(client, '_get_headers', return_value={"Authorization": "Bearer test"}):
            with patch.object(client.token_manager, 'invalidate_token') as mock_invalidate:
                with patch('httpx.AsyncClient') as mock_client_class:
                    mock_client = AsyncMock()
                    mock_client.request = AsyncMock(side_effect=[mock_response_401, mock_response_success])
                    mock_client_class.return_value = mock_client
                    
                    client._client = mock_client
                    
                    result = await client.make_request("GET", "/test")
                    
                    assert result == {"success": True}
                    assert mock_client.request.call_count == 2
                    mock_invalidate.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_max_retries(self, client):
        """Test max retries exceeded."""
        with patch.object(client, '_get_headers', return_value={"Authorization": "Bearer test"}):
            with patch('httpx.AsyncClient') as mock_client_class:
                mock_client = AsyncMock()
                mock_client.request = AsyncMock(side_effect=httpx.HTTPError("Network error"))
                mock_client_class.return_value = mock_client
                
                client._client = mock_client
                
                with pytest.raises(Exception, match="API request failed after 3 attempts"):
                    await client.make_request("GET", "/test")
    
    @pytest.mark.asyncio
    async def test_get_categories(self, client):
        """Test get_categories method."""
        expected_response = {"data": [], "total": 0}
        
        with patch.object(client, 'make_request', return_value=expected_response) as mock_request:
            result = await client.get_categories(
                page_size=25,
                current_item=10,
                order_direction="Desc",
                hierarchical_data=True
            )
            
            assert result == expected_response
            mock_request.assert_called_once_with(
                "GET",
                "/categories",
                data={
                    "pageSize": 25,
                    "currentItem": 10,
                    "orderDirection": "Desc",
                    "hierachicalData": True
                }
            )
    
    @pytest.mark.asyncio
    async def test_get_products(self, client):
        """Test get_products method."""
        expected_response = {"data": [], "total": 0}
        
        with patch.object(client, 'make_request', return_value=expected_response) as mock_request:
            result = await client.get_products(
                page_size=25,
                current_item=10,
                order_direction="Desc",
                category_id=1,
                include_inventory=True
            )
            
            assert result == expected_response
            mock_request.assert_called_once_with(
                "GET",
                "/products",
                data={
                    "pageSize": 25,
                    "currentItem": 10,
                    "orderDirection": "Desc",
                    "categoryId": 1,
                    "includeInventory": True
                }
            )
    
    @pytest.mark.asyncio
    async def test_get_branches(self, client):
        """Test get_branches method."""
        expected_response = {"data": [], "total": 0}
        
        with patch.object(client, 'make_request', return_value=expected_response) as mock_request:
            result = await client.get_branches(
                page_size=25,
                current_item=10,
                order_direction="Desc"
            )
            
            assert result == expected_response
            mock_request.assert_called_once_with(
                "GET",
                "/branches",
                data={
                    "pageSize": 25,
                    "currentItem": 10,
                    "orderDirection": "Desc"
                }
            )
    
    @pytest.mark.asyncio
    async def test_client_not_initialized_error(self, client):
        """Test error when client is not initialized."""
        with pytest.raises(RuntimeError, match="Client not initialized"):
            await client.make_request("GET", "/test")
