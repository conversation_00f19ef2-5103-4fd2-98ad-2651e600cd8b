"""Tests for branches tool."""

import pytest
from unittest.mock import AsyncMock
from src.albatross_kiotviet_mcp.tools.branches.branches_tool import BranchesTool, BranchesToolError


class TestBranchesTool:
    """Test cases for BranchesTool."""
    
    @pytest.fixture
    def branches_tool(self, mock_api_client):
        """Create branches tool instance for testing."""
        return BranchesTool(mock_api_client)
    
    @pytest.mark.asyncio
    async def test_execute_with_defaults(self, branches_tool, mock_api_client, sample_branches_response):
        """Test branches tool execution with default parameters."""
        mock_api_client.get_branches.return_value = sample_branches_response
        
        result = await branches_tool.execute()
        
        assert result == sample_branches_response
        mock_api_client.get_branches.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction='Asc'
        )
    
    @pytest.mark.asyncio
    async def test_execute_with_custom_parameters(self, branches_tool, mock_api_client, sample_branches_response):
        """Test branches tool execution with custom parameters."""
        mock_api_client.get_branches.return_value = sample_branches_response
        
        result = await branches_tool.execute(
            page_size=25,
            current_item=10,
            order_direction='Desc'
        )
        
        assert result == sample_branches_response
        mock_api_client.get_branches.assert_called_once_with(
            page_size=25,
            current_item=10,
            order_direction='Desc'
        )
    
    def test_validate_parameters_valid(self, branches_tool):
        """Test parameter validation with valid parameters."""
        # Should not raise any exception
        branches_tool.validate_parameters(
            page_size=50,
            current_item=0,
            order_direction='Asc'
        )
    
    def test_validate_parameters_invalid_page_size(self, branches_tool):
        """Test parameter validation with invalid page_size."""
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            branches_tool.validate_parameters(page_size=0)
        
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            branches_tool.validate_parameters(page_size=101)
    
    def test_validate_parameters_invalid_current_item(self, branches_tool):
        """Test parameter validation with invalid current_item."""
        with pytest.raises(ValueError, match="current_item must be a non-negative integer"):
            branches_tool.validate_parameters(current_item=-1)
    
    def test_validate_parameters_invalid_order_direction(self, branches_tool):
        """Test parameter validation with invalid order_direction."""
        with pytest.raises(ValueError, match="order_direction must be 'Asc' or 'Desc'"):
            branches_tool.validate_parameters(order_direction='Invalid')
    
    @pytest.mark.asyncio
    async def test_execute_with_validation_error(self, branches_tool):
        """Test branches tool execution with validation error."""
        with pytest.raises(Exception, match="Tool 'get_branches' execution failed"):
            await branches_tool.execute(page_size=0)

    @pytest.mark.asyncio
    async def test_execute_with_api_error(self, branches_tool, mock_api_client):
        """Test branches tool execution with API error."""
        mock_api_client.get_branches.side_effect = Exception("API Error")

        with pytest.raises(Exception, match="Tool 'get_branches' execution failed"):
            await branches_tool.execute()
    
    def test_tool_properties(self, branches_tool):
        """Test tool properties."""
        assert branches_tool.name == "get_branches"
        assert "Get branches from KiotViet API" in branches_tool.description
