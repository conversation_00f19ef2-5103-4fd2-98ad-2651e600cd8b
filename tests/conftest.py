"""Test configuration and fixtures."""

import pytest
from unittest.mock import AsyncMock, MagicMock
from typing import Dict, Any

from src.albatross_kiotviet_mcp.infrastructure.config.settings import KiotVietConfig
from src.albatross_kiotviet_mcp.infrastructure.api.kiotviet_client import KiotVietAPIClient


@pytest.fixture
def mock_config():
    """Mock configuration for testing."""
    # Use environment variables for Pydantic Settings
    import os
    os.environ.update({
        'KIOTVIET_CLIENT_ID': 'test_client_id',
        'KIOTVIET_CLIENT_SECRET': 'test_client_secret',
        'KIOTVIET_RETAILER': 'test_retailer',
        'KIOTVIET_AUTH_URL': 'https://test.auth.url',
        'KIOTVIET_API_BASE_URL': 'https://test.api.url',
        'KIOTVIET_TOKEN_BUFFER_SECONDS': '300',
        'KIOTVIET_REQUEST_TIMEOUT': '30',
        'KIOTVIET_MAX_RETRIES': '3'
    })
    return KiotVietConfig()


@pytest.fixture
def mock_api_client():
    """Mock API client for testing."""
    client = AsyncMock(spec=KiotVietAPIClient)
    
    # Mock context manager methods
    client.__aenter__ = AsyncMock(return_value=client)
    client.__aexit__ = AsyncMock(return_value=None)
    
    # Mock API methods with sample responses
    client.get_categories = AsyncMock(return_value={
        "data": [
            {"id": 1, "name": "Electronics", "description": "Electronic products"},
            {"id": 2, "name": "Clothing", "description": "Clothing items"}
        ],
        "total": 2,
        "pageSize": 50,
        "currentItem": 0
    })
    
    client.get_products = AsyncMock(return_value={
        "data": [
            {"id": 1, "name": "Laptop", "categoryId": 1, "price": 1000},
            {"id": 2, "name": "T-Shirt", "categoryId": 2, "price": 25}
        ],
        "total": 2,
        "pageSize": 50,
        "currentItem": 0
    })
    
    client.get_branches = AsyncMock(return_value={
        "data": [
            {"id": 1, "name": "Main Branch", "address": "123 Main St"},
            {"id": 2, "name": "Branch 2", "address": "456 Oak Ave"}
        ],
        "total": 2,
        "pageSize": 50,
        "currentItem": 0
    })
    
    client.make_request = AsyncMock(return_value={"success": True})
    
    return client


@pytest.fixture
def sample_categories_response():
    """Sample categories API response."""
    return {
        "data": [
            {
                "id": 1,
                "name": "Electronics",
                "description": "Electronic products",
                "parentId": None,
                "isActive": True,
                "createdDate": "2024-01-01T00:00:00Z",
                "modifiedDate": "2024-01-01T00:00:00Z"
            },
            {
                "id": 2,
                "name": "Smartphones",
                "description": "Mobile phones",
                "parentId": 1,
                "isActive": True,
                "createdDate": "2024-01-01T00:00:00Z",
                "modifiedDate": "2024-01-01T00:00:00Z"
            }
        ],
        "total": 2,
        "pageSize": 50,
        "currentItem": 0
    }


@pytest.fixture
def sample_products_response():
    """Sample products API response."""
    return {
        "data": [
            {
                "id": 1,
                "name": "iPhone 15",
                "categoryId": 2,
                "price": 999.99,
                "inventory": {"quantity": 10, "available": 8}
            },
            {
                "id": 2,
                "name": "Samsung Galaxy S24",
                "categoryId": 2,
                "price": 899.99,
                "inventory": {"quantity": 15, "available": 12}
            }
        ],
        "total": 2,
        "pageSize": 50,
        "currentItem": 0
    }


@pytest.fixture
def sample_branches_response():
    """Sample branches API response."""
    return {
        "data": [
            {
                "id": 1,
                "name": "Main Store",
                "address": "123 Main Street, City",
                "phone": "+1234567890",
                "isActive": True
            },
            {
                "id": 2,
                "name": "Branch Store",
                "address": "456 Oak Avenue, City",
                "phone": "+1234567891",
                "isActive": True
            }
        ],
        "total": 2,
        "pageSize": 50,
        "currentItem": 0
    }
