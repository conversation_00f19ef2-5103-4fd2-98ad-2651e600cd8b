"""Tests for products tool."""

import pytest
from unittest.mock import AsyncMock
from src.albatross_kiotviet_mcp.tools.products.products_tool import ProductsTool, ProductsToolError


class TestProductsTool:
    """Test cases for ProductsTool."""
    
    @pytest.fixture
    def products_tool(self, mock_api_client):
        """Create products tool instance for testing."""
        return ProductsTool(mock_api_client)
    
    @pytest.mark.asyncio
    async def test_execute_with_defaults(self, products_tool, mock_api_client, sample_products_response):
        """Test products tool execution with default parameters."""
        mock_api_client.get_products.return_value = sample_products_response
        
        result = await products_tool.execute()
        
        assert result == sample_products_response
        mock_api_client.get_products.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction='Asc',
            category_id=None,
            include_inventory=False
        )
    
    @pytest.mark.asyncio
    async def test_execute_with_custom_parameters(self, products_tool, mock_api_client, sample_products_response):
        """Test products tool execution with custom parameters."""
        mock_api_client.get_products.return_value = sample_products_response
        
        result = await products_tool.execute(
            page_size=25,
            current_item=10,
            order_direction='Desc',
            category_id=1,
            include_inventory=True
        )
        
        assert result == sample_products_response
        mock_api_client.get_products.assert_called_once_with(
            page_size=25,
            current_item=10,
            order_direction='Desc',
            category_id=1,
            include_inventory=True
        )
    
    def test_validate_parameters_valid(self, products_tool):
        """Test parameter validation with valid parameters."""
        # Should not raise any exception
        products_tool.validate_parameters(
            page_size=50,
            current_item=0,
            order_direction='Asc',
            category_id=1
        )
    
    def test_validate_parameters_invalid_page_size(self, products_tool):
        """Test parameter validation with invalid page_size."""
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            products_tool.validate_parameters(page_size=0)
        
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            products_tool.validate_parameters(page_size=101)
    
    def test_validate_parameters_invalid_current_item(self, products_tool):
        """Test parameter validation with invalid current_item."""
        with pytest.raises(ValueError, match="current_item must be a non-negative integer"):
            products_tool.validate_parameters(current_item=-1)
    
    def test_validate_parameters_invalid_order_direction(self, products_tool):
        """Test parameter validation with invalid order_direction."""
        with pytest.raises(ValueError, match="order_direction must be 'Asc' or 'Desc'"):
            products_tool.validate_parameters(order_direction='Invalid')
    
    def test_validate_parameters_invalid_category_id(self, products_tool):
        """Test parameter validation with invalid category_id."""
        with pytest.raises(ValueError, match="category_id must be a positive integer"):
            products_tool.validate_parameters(category_id=0)
        
        with pytest.raises(ValueError, match="category_id must be a positive integer"):
            products_tool.validate_parameters(category_id=-1)
    
    @pytest.mark.asyncio
    async def test_execute_with_validation_error(self, products_tool):
        """Test products tool execution with validation error."""
        with pytest.raises(Exception, match="Tool 'get_products' execution failed"):
            await products_tool.execute(page_size=0)

    @pytest.mark.asyncio
    async def test_execute_with_api_error(self, products_tool, mock_api_client):
        """Test products tool execution with API error."""
        mock_api_client.get_products.side_effect = Exception("API Error")

        with pytest.raises(Exception, match="Tool 'get_products' execution failed"):
            await products_tool.execute()
    
    def test_tool_properties(self, products_tool):
        """Test tool properties."""
        assert products_tool.name == "get_products"
        assert "Get products from KiotViet API" in products_tool.description
