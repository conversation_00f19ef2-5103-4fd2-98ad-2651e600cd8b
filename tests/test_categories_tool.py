"""Tests for categories tool."""

import pytest
from unittest.mock import AsyncMock
from src.albatross_kiotviet_mcp.tools.categories.categories_tool import CategoriesTool, CategoriesToolError


class TestCategoriesTool:
    """Test cases for CategoriesTool."""
    
    @pytest.fixture
    def categories_tool(self, mock_api_client):
        """Create categories tool instance for testing."""
        return CategoriesTool(mock_api_client)
    
    @pytest.mark.asyncio
    async def test_execute_with_defaults(self, categories_tool, mock_api_client, sample_categories_response):
        """Test categories tool execution with default parameters."""
        mock_api_client.get_categories.return_value = sample_categories_response
        
        result = await categories_tool.execute()
        
        assert result == sample_categories_response
        mock_api_client.get_categories.assert_called_once_with(
            page_size=50,
            current_item=0,
            order_direction='Asc',
            hierarchical_data=False
        )
    
    @pytest.mark.asyncio
    async def test_execute_with_custom_parameters(self, categories_tool, mock_api_client, sample_categories_response):
        """Test categories tool execution with custom parameters."""
        mock_api_client.get_categories.return_value = sample_categories_response
        
        result = await categories_tool.execute(
            page_size=25,
            current_item=10,
            order_direction='Desc',
            hierarchical_data=True
        )
        
        assert result == sample_categories_response
        mock_api_client.get_categories.assert_called_once_with(
            page_size=25,
            current_item=10,
            order_direction='Desc',
            hierarchical_data=True
        )
    
    def test_validate_parameters_valid(self, categories_tool):
        """Test parameter validation with valid parameters."""
        # Should not raise any exception
        categories_tool.validate_parameters(
            page_size=50,
            current_item=0,
            order_direction='Asc'
        )
    
    def test_validate_parameters_invalid_page_size(self, categories_tool):
        """Test parameter validation with invalid page_size."""
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            categories_tool.validate_parameters(page_size=0)
        
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            categories_tool.validate_parameters(page_size=101)
        
        with pytest.raises(ValueError, match="page_size must be an integer between 1 and 100"):
            categories_tool.validate_parameters(page_size="invalid")
    
    def test_validate_parameters_invalid_current_item(self, categories_tool):
        """Test parameter validation with invalid current_item."""
        with pytest.raises(ValueError, match="current_item must be a non-negative integer"):
            categories_tool.validate_parameters(current_item=-1)
        
        with pytest.raises(ValueError, match="current_item must be a non-negative integer"):
            categories_tool.validate_parameters(current_item="invalid")
    
    def test_validate_parameters_invalid_order_direction(self, categories_tool):
        """Test parameter validation with invalid order_direction."""
        with pytest.raises(ValueError, match="order_direction must be 'Asc' or 'Desc'"):
            categories_tool.validate_parameters(order_direction='Invalid')
    
    @pytest.mark.asyncio
    async def test_execute_with_validation_error(self, categories_tool):
        """Test categories tool execution with validation error."""
        with pytest.raises(Exception, match="Tool 'get_categories' execution failed"):
            await categories_tool.execute(page_size=0)
    
    @pytest.mark.asyncio
    async def test_execute_with_api_error(self, categories_tool, mock_api_client):
        """Test categories tool execution with API error."""
        mock_api_client.get_categories.side_effect = Exception("API Error")

        with pytest.raises(Exception, match="Tool 'get_categories' execution failed"):
            await categories_tool.execute()
    
    def test_tool_properties(self, categories_tool):
        """Test tool properties."""
        assert categories_tool.name == "get_categories"
        assert "Get product categories from KiotViet API" in categories_tool.description
