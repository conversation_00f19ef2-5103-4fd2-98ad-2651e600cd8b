"""Tests for token manager."""

import pytest
from unittest.mock import As<PERSON>M<PERSON>, patch, MagicMock
from datetime import datetime, timedelta
import httpx
from src.albatross_kiotviet_mcp.infrastructure.auth.token_manager import TokenManager


class TestTokenManager:
    """Test cases for TokenManager."""
    
    @pytest.fixture
    def token_manager(self, mock_config):
        """Create token manager instance for testing."""
        return TokenManager(mock_config)
    
    def test_is_token_valid_no_token(self, token_manager):
        """Test token validation when no token exists."""
        assert not token_manager._is_token_valid()
    
    def test_is_token_valid_expired(self, token_manager):
        """Test token validation when token is expired."""
        token_manager._access_token = "test_token"
        token_manager._token_expires_at = datetime.now() - timedelta(minutes=1)
        
        assert not token_manager._is_token_valid()
    
    def test_is_token_valid_expiring_soon(self, token_manager):
        """Test token validation when token is expiring within buffer time."""
        token_manager._access_token = "test_token"
        # Token expires in 2 minutes, but buffer is 5 minutes
        token_manager._token_expires_at = datetime.now() + timedelta(minutes=2)
        
        assert not token_manager._is_token_valid()
    
    def test_is_token_valid_good(self, token_manager):
        """Test token validation when token is valid."""
        token_manager._access_token = "test_token"
        # Token expires in 10 minutes, buffer is 5 minutes
        token_manager._token_expires_at = datetime.now() + timedelta(minutes=10)
        
        assert token_manager._is_token_valid()
    
    @pytest.mark.asyncio
    async def test_refresh_token_success(self, token_manager):
        """Test successful token refresh."""
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "access_token": "new_test_token",
            "expires_in": 3600
        }
        mock_response.raise_for_status = MagicMock()
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.post = AsyncMock(return_value=mock_response)
            mock_client.__aenter__ = AsyncMock(return_value=mock_client)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            mock_client_class.return_value = mock_client
            
            await token_manager._refresh_token()
            
            assert token_manager._access_token == "new_test_token"
            assert token_manager._token_expires_at is not None
            
            # Check that the token expires approximately 1 hour from now
            expected_expiry = datetime.now() + timedelta(seconds=3600)
            time_diff = abs((token_manager._token_expires_at - expected_expiry).total_seconds())
            assert time_diff < 5  # Allow 5 seconds tolerance
    
    @pytest.mark.asyncio
    async def test_refresh_token_http_error(self, token_manager):
        """Test token refresh with HTTP error."""
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.post = AsyncMock(side_effect=httpx.HTTPError("Network error"))
            mock_client.__aenter__ = AsyncMock(return_value=mock_client)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            mock_client_class.return_value = mock_client
            
            with pytest.raises(Exception, match="Authentication failed"):
                await token_manager._refresh_token()
    
    @pytest.mark.asyncio
    async def test_refresh_token_invalid_response(self, token_manager):
        """Test token refresh with invalid response format."""
        mock_response = MagicMock()
        mock_response.json.return_value = {"invalid": "response"}
        mock_response.raise_for_status = MagicMock()
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client.post = AsyncMock(return_value=mock_response)
            mock_client.__aenter__ = AsyncMock(return_value=mock_client)
            mock_client.__aexit__ = AsyncMock(return_value=None)
            mock_client_class.return_value = mock_client
            
            with pytest.raises(Exception, match="Invalid authentication response"):
                await token_manager._refresh_token()
    
    @pytest.mark.asyncio
    async def test_get_access_token_valid_token(self, token_manager):
        """Test getting access token when current token is valid."""
        token_manager._access_token = "valid_token"
        token_manager._token_expires_at = datetime.now() + timedelta(minutes=10)
        
        token = await token_manager.get_access_token()
        
        assert token == "valid_token"
    
    @pytest.mark.asyncio
    async def test_get_access_token_refresh_needed(self, token_manager):
        """Test getting access token when refresh is needed."""
        # Mock the refresh method
        with patch.object(token_manager, '_refresh_token') as mock_refresh:
            token_manager._access_token = "refreshed_token"
            
            token = await token_manager.get_access_token()
            
            assert token == "refreshed_token"
            mock_refresh.assert_called_once()
    
    def test_invalidate_token(self, token_manager):
        """Test token invalidation."""
        token_manager._access_token = "test_token"
        token_manager._token_expires_at = datetime.now() + timedelta(minutes=10)
        
        token_manager.invalidate_token()
        
        assert token_manager._access_token is None
        assert token_manager._token_expires_at is None
