"""Tests for configuration."""

import pytest
import os
from unittest.mock import patch
from pydantic import ValidationError
from src.albatross_kiotviet_mcp.infrastructure.config.settings import KiotVietConfig, get_config


class TestKiotVietConfig:
    """Test cases for KiotVietConfig."""
    
    @patch.dict(os.environ, {
        'KIOTVIET_CLIENT_ID': 'test_client_id',
        'KIOTVIET_CLIENT_SECRET': 'test_client_secret',
        'KIOTVIET_RETAILER': 'test_retailer'
    })
    def test_config_with_required_fields(self):
        """Test configuration with all required fields."""
        config = KiotVietConfig()

        assert config.client_id == "test_client_id"
        assert config.client_secret == "test_client_secret"
        assert config.retailer == "test_retailer"

        # Check defaults
        assert config.auth_url == "https://id.kiotviet.vn/connect/token"
        assert config.api_base_url == "https://public.kiotapi.com"
        assert config.token_buffer_seconds == 300
        assert config.request_timeout == 30
        assert config.max_retries == 3
    
    @patch.dict(os.environ, {
        'KIOTVIET_CLIENT_ID': 'test_client_id',
        'KIOTVIET_CLIENT_SECRET': 'test_client_secret',
        'KIOTVIET_RETAILER': 'test_retailer',
        'KIOTVIET_AUTH_URL': 'https://custom.auth.url',
        'KIOTVIET_API_BASE_URL': 'https://custom.api.url',
        'KIOTVIET_TOKEN_BUFFER_SECONDS': '600',
        'KIOTVIET_REQUEST_TIMEOUT': '60',
        'KIOTVIET_MAX_RETRIES': '5'
    })
    def test_config_with_custom_values(self):
        """Test configuration with custom values."""
        config = KiotVietConfig()

        assert config.auth_url == "https://custom.auth.url"
        assert config.api_base_url == "https://custom.api.url"
        assert config.token_buffer_seconds == 600
        assert config.request_timeout == 60
        assert config.max_retries == 5
    
    def test_config_missing_required_fields(self):
        """Test configuration with missing required fields."""
        with pytest.raises(ValidationError):
            KiotVietConfig()
        
        with pytest.raises(ValidationError):
            KiotVietConfig(client_id="test_id")
        
        with pytest.raises(ValidationError):
            KiotVietConfig(
                client_id="test_id",
                client_secret="test_secret"
            )
    
    @patch.dict(os.environ, {
        'KIOTVIET_CLIENT_ID': 'env_client_id',
        'KIOTVIET_CLIENT_SECRET': 'env_client_secret',
        'KIOTVIET_RETAILER': 'env_retailer',
        'KIOTVIET_AUTH_URL': 'https://env.auth.url',
        'KIOTVIET_API_BASE_URL': 'https://env.api.url',
        'KIOTVIET_TOKEN_BUFFER_SECONDS': '600',
        'KIOTVIET_REQUEST_TIMEOUT': '60',
        'KIOTVIET_MAX_RETRIES': '5'
    })
    def test_config_from_environment(self):
        """Test configuration loading from environment variables."""
        config = KiotVietConfig()
        
        assert config.client_id == "env_client_id"
        assert config.client_secret == "env_client_secret"
        assert config.retailer == "env_retailer"
        assert config.auth_url == "https://env.auth.url"
        assert config.api_base_url == "https://env.api.url"
        assert config.token_buffer_seconds == 600
        assert config.request_timeout == 60
        assert config.max_retries == 5
    
    @patch.dict(os.environ, {
        'KIOTVIET_CLIENT_ID': 'env_client_id',
        'KIOTVIET_CLIENT_SECRET': 'env_client_secret',
        'KIOTVIET_RETAILER': 'env_retailer'
    })
    def test_get_config_function(self):
        """Test get_config function."""
        config = get_config()
        
        assert isinstance(config, KiotVietConfig)
        assert config.client_id == "env_client_id"
        assert config.client_secret == "env_client_secret"
        assert config.retailer == "env_retailer"
