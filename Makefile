.PHONY: help install install-dev test test-cov lint format type-check clean build docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install production dependencies"
	@echo "  install-dev  - Install development dependencies"
	@echo "  test         - Run tests"
	@echo "  test-cov     - Run tests with coverage"
	@echo "  lint         - Run linting"
	@echo "  format       - Format code"
	@echo "  type-check   - Run type checking"
	@echo "  clean        - Clean build artifacts"
	@echo "  build        - Build package"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run with Docker Compose"

# Installation
install:
	uv sync

install-dev:
	uv sync --extra dev --extra test

# Testing
test:
	pytest

test-cov:
	pytest --cov=src --cov-report=html --cov-report=term

# Code quality
lint:
	flake8 src tests

format:
	black src tests
	isort src tests

type-check:
	mypy src

# Cleanup
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf htmlcov/

# Build
build:
	uv build

# Docker
docker-build:
	docker build -t albatross-kiotviet-mcp .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

# Development server
dev:
	fastmcp dev src/albatross_kiotviet_mcp/server.py

# Run server
run:
	uv run albatross-kiotviet-mcp-server
