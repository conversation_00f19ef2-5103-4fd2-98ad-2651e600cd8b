# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2025-01-20

### Added
- Initial release of Albatross KiotViet MCP Server
- KiotViet Public API integration with automatic token management
- `get_categories` tool for retrieving product categories
- Comprehensive error handling and retry logic
- Production-ready configuration management
- FastMCP integration for AI tools
- Claude Desktop integration support
- Comprehensive documentation and examples

### Features
- **Authentication**: Automatic token refresh with expiry handling
- **API Client**: Clean, async HTTP client with retry logic
- **Configuration**: Environment-based configuration with validation
- **MCP Integration**: FastMCP server with tool definitions
- **Error Handling**: Comprehensive error handling and logging
- **Extensibility**: Easy to add new KiotViet API endpoints

### Technical Details
- Python 3.10+ support
- Async/await throughout
- Pydantic v2 for configuration validation
- httpx for HTTP client
- FastMCP for MCP server functionality