# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Testing
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Git
.git/
.gitignore

# Documentation
docs/
*.md
!README.md

# Development files
.pre-commit-config.yaml
Makefile
docker-compose.yml
docker-compose.*.yml

# Test files
tests/
pytest.ini
.coveragerc
